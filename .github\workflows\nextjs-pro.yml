# Workflow for building and running Next.js project with Bun
#
name: Upload Main Version

on:
  # Runs on pushes targeting the pre branch
  push:
    branches:
      - main

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets basic permissions
permissions:
  contents: read

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
concurrency:
  group: "build-and-run-pro"
  cancel-in-progress: false

jobs:
  # Build and run job
  build-and-run-pro:
    runs-on: [self-hosted, Linux, X64, pro]
    environment:
      name: pro
    env:
      NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
      NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
      AZURE_AD_CLIENT_ID: ${{ secrets.AZURE_AD_CLIENT_ID }}
      AZURE_AD_CLIENT_SECRET: ${{ secrets.AZURE_AD_CLIENT_SECRET }}
      AZURE_AD_TENANT_ID: ${{ secrets.AZURE_AD_TENANT_ID }}
      BACKEND_URL: ${{ secrets.BACKEND_URL }}
      NEXT_PUBLIC_BACKEND_URL: ${{ secrets.NEXT_PUBLIC_BACKEND_URL }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun environment
        run: |
          source ~/.bashrc
          echo "$HOME/.bun/bin" >> $GITHUB_PATH

      - name: Cache Bun dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.bun/install/cache
            node_modules
            .next/cache
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb', '**/package.json') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
          restore-keys: |
            ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb', '**/package.json') }}-

      - name: Install dependencies
        run: bun install

      - name: Build Next.js project
        run: bun run build

      - name: Start Next.js application
        run: |
          pm2 describe web-pre > /dev/null
          if [ $? -eq 0 ]; then
            pm2 restart web-pre
          else
            pm2 start "bun run start" --name web-pro
          fi

      - name: Wait for application to start
        run: |
          echo "Waiting for application to start..."
          sleep 10

      - name: Health check
        run: |
          echo "Performing health check..."
          curl -f http://localhost:3000
          echo "Application is running successfully!"
