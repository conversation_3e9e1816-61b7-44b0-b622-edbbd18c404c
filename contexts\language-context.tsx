"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useLanguages } from '@/hooks/use-languages'

interface LanguageContextType {
  selectedLanguage: string
  setSelectedLanguage: (language: string) => void
  languages: Array<{ language: string; language_display: string }>
  loading: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

interface LanguageProviderProps {
  children: ReactNode
  defaultLanguage?: string
}

export function LanguageProvider({ children, defaultLanguage = "es" }: LanguageProviderProps) {
  const { languages, loading } = useLanguages()
  const [selectedLanguage, setSelectedLanguage] = useState<string>(defaultLanguage)

  // Set default language when languages load
  useEffect(() => {
    if (languages.length > 0 && !selectedLanguage) {
      setSelectedLanguage(languages[0].language)
    }
  }, [languages, selectedLanguage])

  const value: LanguageContextType = {
    selectedLanguage,
    setSelectedLanguage,
    languages,
    loading
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguageContext() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguageContext must be used within a LanguageProvider')
  }
  return context
}
