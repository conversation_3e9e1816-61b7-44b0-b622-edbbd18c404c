"use client"

import * as React from "react"
import { use<PERSON>emo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, Globe } from "lucide-react"
import { cn } from "@/lib/utils"
import { NewsletterBlock, NewsletterHeaderFooter } from "@/types/newsletter"
import { useLanguageContext } from "@/contexts/language-context"

interface HTMLPreviewProps {
  blocks: NewsletterBlock[]
  headers?: NewsletterHeaderFooter[]
  footers?: NewsletterHeaderFooter[]
  className?: string
}

export function HTMLPreview({ blocks, headers = [], footers = [], className }: HTMLPreviewProps) {
  const { selectedLanguage, setSelectedLanguage, languages, loading: languagesLoading } = useLanguageContext()

  // Build the complete HTML preview
  const previewHtml = useMemo(() => {
    const safeHeaders = headers || []
    const safeBlocks = blocks || []
    const safeFooters = footers || []

    let html = ""

    // Add headers first
    for (const header of safeHeaders) {
      let content = header.html_content || ""

      // Replace variables with their values in the selected language
      if (header.variable_values && Array.isArray(header.variable_values)) {
        header.variable_values.forEach((variable) => {
          const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
          const languageValue = variable.value[selectedLanguage as keyof typeof variable.value] || ''
          content = content.replace(regex, languageValue)
        })
      }

      html += `\n<!-- Header: ${header.name} -->\n`
      html += content + "\n"
    }

    // Add visible blocks sorted by order_position
    const visibleBlocks = safeBlocks
      .filter(b => b?.is_visible)
      .sort((a, b) => (a?.order_position || 0) - (b?.order_position || 0))

    for (const block of visibleBlocks) {
      let content = block.html_content || ""

      // Replace variables with their values in the selected language
      if (block.variable_values && Array.isArray(block.variable_values)) {
        block.variable_values.forEach((variable) => {
          const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
          const languageValue = variable.value[selectedLanguage as keyof typeof variable.value] || ''
          content = content.replace(regex, languageValue)
        })
      }

      html += `\n<!-- Block: ${block.name} (#${block.order_position}) -->\n`
      html += content + "\n"
    }

    // Add footers last
    for (const footer of safeFooters) {
      let content = footer.html_content || ""

      // Replace variables with their values in the selected language
      if (footer.variable_values && Array.isArray(footer.variable_values)) {
        footer.variable_values.forEach((variable) => {
          const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
          const languageValue = variable.value[selectedLanguage as keyof typeof variable.value] || ''
          content = content.replace(regex, languageValue)
        })
      }

      html += `\n<!-- Footer: ${footer.name} -->\n`
      html += content + "\n"
    }

    return html
  }, [blocks, headers, footers, selectedLanguage])

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            <CardTitle>Vista prèvia en directe</CardTitle>
          </div>

          <div className="flex items-center gap-2">
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage} disabled={languagesLoading || languages.length === 0}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="es">
                  <div className="flex items-center gap-2">
                    <Globe className="h-3 w-3 text-red-500" />
                    Espanyol
                  </div>
                </SelectItem>
                <SelectItem value="ca">
                  <div className="flex items-center gap-2">
                    <Globe className="h-3 w-3 text-yellow-600" />
                    Català
                  </div>
                </SelectItem>
                <SelectItem value="fr">
                  <div className="flex items-center gap-2">
                    <Globe className="h-3 w-3 text-green-500" />
                    Francès
                  </div>
                </SelectItem>
                <SelectItem value="en">
                  <div className="flex items-center gap-2">
                    <Globe className="h-3 w-3 text-blue-500" />
                    Anglès
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="border rounded-lg m-4 h-full">
        <div className="bg-white min-w-[320px] overflow-y-auto h-full">
          {
            previewHtml ? (
                          <div className="h-full" dangerouslySetInnerHTML={{ __html: previewHtml }} />
            ) : (
              <div className="text-sm text-muted-foreground">No hi ha blocs per mostrar</div>
            )
          }
        </div>
      </CardContent>
    </Card>
  )
}
